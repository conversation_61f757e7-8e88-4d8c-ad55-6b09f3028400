'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Edit, Play, Save, X } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import type React from 'react';
import { useEffect, use as usePromise, useState } from 'react';
import { toast } from 'sonner';
import { JobTimeline } from '@/components/timeline/JobTimeline';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useJobProcessing } from '@/lib/hooks/use-job-processing';
import { getSourceDisplayName } from '@/lib/job-board-constants';
import { JobExtractionSchema } from '@/lib/job-schema';
import type { JobStatus } from '@/lib/job-status';
import type { DatabaseJob } from '@/lib/storage';
import { logger } from '@/lib/utils';

interface JobDetailPageProps {
  params: Promise<{ id: string }>;
}

// Fields that are processed and enriched by AI (derived automatically for 100% accuracy)
const AI_PROCESSED_FIELDS = new Set(
  // Using Object.keys on the zod schema shape ensures the list stays up to date
  // with any future changes to JobExtractionSchema without manual edits.
  Object.keys(JobExtractionSchema.shape)
);

const JobDetailPage: React.FC<JobDetailPageProps> = ({ params }) => {
  const router = useRouter();
  const [job, setJob] = useState<DatabaseJob | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<Partial<DatabaseJob>>({});
  const [saving, setSaving] = useState(false);

  const resolvedParams = usePromise(params);
  const jobId = resolvedParams.id;

  // Process Now functionality
  const { processJob, isProcessing, getError, clearError } = useJobProcessing();

  useEffect(() => {
    const fetchJob = async () => {
      try {
        const response = await fetch(`/api/jobs/${jobId}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP ${response.status}`);
        }

        const data = await response.json();
        setJob(data.job);
        setEditForm(data.job);

        logger.info('Job loaded successfully', { jobId });
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to load job';
        setError(errorMessage);
        logger.error('Failed to load job:', errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchJob();
  }, [jobId]);

  const handleEditToggle = () => {
    if (isEditing) {
      // Reset form to current job data when canceling
      setEditForm(job || {});
    }
    setIsEditing(!isEditing);
  };

  const handleSave = async () => {
    if (!job) {
      return;
    }

    setSaving(true);
    try {
      const response = await fetch(`/api/jobs/${jobId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update job');
      }

      const data = await response.json();
      setJob(data.job);
      setEditForm(data.job);
      setIsEditing(false);

      logger.info('Job updated successfully', { jobId });
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to update job';
      setError(errorMessage);
      logger.error('Failed to update job:', errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (
    field: keyof DatabaseJob,
    value: string | boolean | null
  ) => {
    setEditForm((prev: Partial<DatabaseJob>) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleProcessNow = async () => {
    if (!job) {
      return;
    }

    // Show loading toast
    const loadingToast = toast.loading('Starting AI job processing...', {
      description: 'Initializing extraction pipeline',
    });

    try {
      await processJob(job.id, () => {
        // This callback will be called when processing completes
        // We'll handle the refresh after showing the success toast
      });

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show success toast
      toast.success('Job processing completed successfully!', {
        description: 'AI extraction finished • Page will refresh in 3 seconds',
        duration: 4000,
        action: {
          label: 'Refresh Now',
          onClick: () => window.location.reload(),
        },
      });

      // Refresh after a delay so user can read the toast
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    } catch (_processError) {
      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show error toast
      toast.error('Failed to process job', {
        description: 'Please try again or check the job details for issues',
        duration: 6000,
        action: {
          label: 'Retry',
          onClick: () => handleProcessNow(),
        },
      });
    }
  };

  const formatValue = (value: unknown): string => {
    if (value === null || value === undefined) {
      return 'N/A';
    }
    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    }
    if (Array.isArray(value)) {
      return value.join(', ');
    }
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  const _getJobStatusBadgeVariant = (status: JobStatus) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'draft':
        return 'secondary';
      case 'inactive':
        return 'outline';
      case 'expired':
        return 'destructive';
      case 'filled':
        return 'default';
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getJobStatusBadgeClass = (status: JobStatus) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'expired':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'filled':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getProcessingStatusBadgeClass = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'pending':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getProcessingStatusText = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'AI Processed';
      case 'pending':
        return 'AI Pending';
      case 'failed':
        return 'AI Failed';
      default:
        return 'AI Unknown';
    }
  };

  // Check if a field was enriched by AI
  const isAIEnriched = (fieldKey: string): boolean => {
    return (
      AI_PROCESSED_FIELDS.has(fieldKey) &&
      job?.processing_status === 'completed'
    );
  };

  const renderField = (key: string, _label: string, value: unknown) => {
    const isEditMode =
      isEditing && key !== 'id' && key !== 'created_at' && key !== 'updated_at';

    if (isEditMode) {
      const editValue = editForm[key as keyof DatabaseJob];

      if (typeof value === 'boolean') {
        return (
          <select
            className="w-full rounded border border-gray-300 px-2 py-1 text-xs"
            onChange={(e) =>
              handleInputChange(
                key as keyof DatabaseJob,
                e.target.value === 'true'
              )
            }
            value={String(editValue)}
          >
            <option value="true">Yes</option>
            <option value="false">No</option>
          </select>
        );
      }

      if (
        key === 'description' ||
        key === 'responsibilities' ||
        key === 'benefits'
      ) {
        return (
          <textarea
            className="w-full rounded border border-gray-300 px-2 py-1 text-xs"
            onChange={(e) =>
              handleInputChange(key as keyof DatabaseJob, e.target.value)
            }
            rows={4}
            value={String(editValue || '')}
          />
        );
      }

      return (
        <input
          className="w-full rounded border border-gray-300 px-2 py-1 text-xs"
          onChange={(e) =>
            handleInputChange(key as keyof DatabaseJob, e.target.value)
          }
          type="text"
          value={String(editValue || '')}
        />
      );
    }

    return (
      <div className="whitespace-pre-wrap break-words rounded bg-gray-50 p-2 text-xs">
        {formatValue(value)}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-4xl px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="text-gray-500">Loading job details...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-4xl px-4 py-8">
          <div className="rounded-lg bg-red-50 p-6">
            <div className="text-red-800">Error: {error}</div>
            <div className="mt-4 space-x-3">
              <button
                className="rounded bg-red-600 px-4 py-2 text-white hover:bg-red-700"
                onClick={() => window.location.reload()}
                type="button"
              >
                Retry
              </button>
              <button
                className="rounded border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50"
                onClick={() => router.back()}
                type="button"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-4xl px-4 py-8">
          <div className="text-center">
            <div className="text-gray-500">Job not found</div>
            <button
              className="mt-4 rounded border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50"
              onClick={() => router.back()}
              type="button"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-4xl px-4 py-8">
        {/* Header */}
        <div className="mb-6">
          <div className="mb-4 flex items-center justify-between">
            <Button
              className="-ml-2"
              onClick={() => router.back()}
              size="sm"
              variant="outline"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Jobs
            </Button>
            <div className="flex items-center gap-2">
              {isEditing ? (
                <>
                  <Button
                    onClick={handleEditToggle}
                    size="sm"
                    variant="outline"
                  >
                    <X className="mr-2 h-4 w-4" />
                    Cancel
                  </Button>
                  <Button disabled={saving} onClick={handleSave} size="sm">
                    <Save className="mr-2 h-4 w-4" />
                    {saving ? 'Saving...' : 'Save Changes'}
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    disabled={isProcessing(jobId)}
                    onClick={handleProcessNow}
                    size="sm"
                    variant="outline"
                  >
                    <Play className="mr-2 h-4 w-4" />
                    {isProcessing(jobId) ? 'Processing...' : 'Process Now'}
                  </Button>
                  <Button onClick={handleEditToggle} size="sm">
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Job
                  </Button>
                </>
              )}
            </div>
          </div>
          <div>
            <h1 className="mb-3 font-bold text-2xl text-gray-900">
              {job.title}
            </h1>
            <div className="mb-2 flex items-center gap-2">
              <Badge
                className={`capitalize ${getJobStatusBadgeClass(job.status)}`}
              >
                {job.status}
              </Badge>
              <Badge
                className={`text-xs ${getProcessingStatusBadgeClass(
                  job.processing_status
                )}`}
              >
                {getProcessingStatusText(job.processing_status)}
              </Badge>
              {job.source_url && (
                <Badge
                  className="flex items-center gap-1 text-xs"
                  variant="outline"
                >
                  <Image
                    alt={`${
                      job.source_name || new URL(job.source_url).hostname
                    } favicon`}
                    className="h-3 w-3 rounded-full"
                    height={12}
                    onError={() => {
                      // Next.js Image handles errors internally
                    }}
                    src={`https://www.google.com/s2/favicons?domain=${
                      new URL(job.source_url).hostname
                    }&sz=32`}
                    width={12}
                  />
                  <span>
                    {(job.source_name === 'manual_entry' ||
                      job.source_name === null) &&
                    job.source_type
                      ? getSourceDisplayName(job.source_type)
                      : job.source_name || new URL(job.source_url).hostname}
                  </span>
                </Badge>
              )}
            </div>
            <p className="text-gray-600">
              {job.company} • Job ID: {job.id}
            </p>
            {getError(jobId) && (
              <div className="mt-2 rounded-md bg-red-50 p-2">
                <div className="flex items-center justify-between">
                  <p className="text-red-700 text-sm">
                    Processing error: {getError(jobId)}
                  </p>
                  <Button
                    onClick={() => clearError(jobId)}
                    size="sm"
                    variant="ghost"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Job Details */}
        <div className="rounded-lg bg-white shadow">
          <div className="border-gray-200 border-b px-6 py-4">
            <h2 className="font-semibold text-gray-900 text-lg">Job Details</h2>
            {isEditing && (
              <p className="mt-1 text-gray-600 text-sm">
                Make changes below and click "Save Changes" to update the job.
              </p>
            )}
          </div>

          <div className="p-6">
            {/* Unified view of all fields */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {Object.entries(job).map(([fieldKey, fieldVal]) => (
                <div key={fieldKey}>
                  <div className="mb-1 flex items-center gap-2">
                    <span className="block font-medium text-gray-700 text-xs">
                      {fieldKey}
                    </span>
                    {isAIEnriched(fieldKey) && (
                      <Badge
                        className="border-blue-200 bg-blue-100 px-1 py-0 text-[10px] text-blue-800"
                        variant="outline"
                      >
                        AI
                      </Badge>
                    )}
                  </div>
                  {renderField(fieldKey, fieldKey, fieldVal as unknown)}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Timeline Section */}
        <div className="mt-8">
          <JobTimeline job={job} />
        </div>
      </div>
    </div>
  );
};

export default JobDetailPage;
