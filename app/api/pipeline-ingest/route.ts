import { Receiver } from '@upstash/qstash';
import { type NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
// UPSTASH_CONFIG removed - using direct env vars for better serverless compatibility
import { extractJob } from '@/lib/extract-job';
import { logger } from '@/lib/logger';

import { saveProcessedJob } from '@/lib/storage';
import { tagJob } from '@/lib/tagging-engine';
import type { JobData } from '@/lib/types';
import { getMemoryUsage } from '@/lib/utils';

// QStash client initialization moved to function level for serverless compatibility

// QStash receiver initialization moved inside function to ensure env vars are available

// Schema for incoming pipeline data
const PipelineJobSchema = z.object({
  content: z.string().min(1, 'Job content is required'),
  sourceUrl: z.string().url('Valid source URL required'),
  source: z.string().optional().default('pipeline'),
  metadata: z
    .object({
      company: z.string().optional(),
      title: z.string().optional(),
      postedDate: z.string().optional(),
      batchId: z.string().optional(),
    })
    .optional()
    .default({}),
});

const PipelineBatchSchema = z.object({
  jobs: z.array(PipelineJobSchema).min(1, 'At least one job required'),
  batchId: z.string().optional(),
  priority: z.enum(['low', 'normal', 'high']).optional().default('normal'),
});

type PipelineJob = z.infer<typeof PipelineJobSchema>;
type PipelineBatch = z.infer<typeof PipelineBatchSchema>;

interface ExtractionMeta {
  cost?: { total?: number };
  duration?: number;
  [key: string]: unknown;
}

interface ProcessingResult {
  jobId: string;
  status: 'success';
  extractedJob: JobData;
  metadata: ExtractionMeta;
  savedToDatabase: boolean;
  databaseId: string | null;
}

interface ProcessingError {
  jobId: string;
  status: 'error';
  error: string;
  sourceUrl: string;
}

interface ProcessingDuplicate {
  jobId: string;
  status: 'skipped';
  reason: 'duplicate';
  duplicateInfo: {
    reason: 'apply_url' | 'source_url' | 'content' | null;
    confidence: 'high' | 'medium' | 'low';
    layer: 'level1' | 'level2' | 'level3';
  };
  title: string;
  company: string | null;
  sourceUrl: string;
}

/**
 * Creates default JobData structure with fallback values
 * Broken down from complex function to reduce cognitive complexity
 */
function createJobDataDefaults(): JobData {
  return {
    sourcedAt: new Date().toISOString(),
    sourceUrl: '',
    title: 'Untitled Job',
    description: '',
    company: null,
    type: null,
    apply_url: '',
    apply_method: null,
    posted_date: null,
    status: 'active',
    salary_min: null,
    salary_max: null,
    salary_currency: null,
    salary_unit: null,
    workplace_type: null,
    remote_region: null,
    timezone_requirements: null,
    workplace_city: null,
    workplace_country: null,
    benefits: null,
    application_requirements: null,
    valid_through: null,
    job_identifier: null,
    job_source_name: null,
    source_name: null,
    department: null,
    travel_required: null,
    career_level: null,
    visa_sponsorship: null,
    languages: null,
    skills: null,
    qualifications: null,
    education_requirements: null,
    experience_requirements: null,
    responsibilities: null,
    featured: null,
    industry: null,
    occupational_category: null,
  };
}

/**
 * Normalizes basic job information fields
 */
function normalizeBasicJobInfo(
  job: Partial<JobData>,
  defaults: JobData,
  sourceUrl: string
): Partial<JobData> {
  return {
    sourcedAt: new Date().toISOString(),
    sourceUrl: sourceUrl || '',
    title: job.title || defaults.title,
    description: job.description || defaults.description,
    company: job.company ?? defaults.company,
    type: job.type ?? defaults.type,
    apply_url: job.apply_url || sourceUrl,
    apply_method: job.apply_method ?? defaults.apply_method,
    posted_date: job.posted_date ?? defaults.posted_date,
    status: job.status || defaults.status,
  };
}

/**
 * Normalizes salary and compensation fields
 */
function normalizeSalaryInfo(
  job: Partial<JobData>,
  defaults: JobData
): Partial<JobData> {
  return {
    salary_min: job.salary_min ?? defaults.salary_min,
    salary_max: job.salary_max ?? defaults.salary_max,
    salary_currency: job.salary_currency ?? defaults.salary_currency,
    salary_unit: job.salary_unit ?? defaults.salary_unit,
  };
}

/**
 * Normalizes workplace and location fields
 */
function normalizeWorkplaceInfo(
  job: Partial<JobData>,
  defaults: JobData
): Partial<JobData> {
  return {
    workplace_type: job.workplace_type ?? defaults.workplace_type,
    remote_region: job.remote_region ?? defaults.remote_region,
    timezone_requirements:
      job.timezone_requirements ?? defaults.timezone_requirements,
    workplace_city: job.workplace_city ?? defaults.workplace_city,
    workplace_country: job.workplace_country ?? defaults.workplace_country,
  };
}

/**
 * Normalizes application and job metadata fields
 */
function normalizeJobMetadataInfo(
  job: Partial<JobData>,
  defaults: JobData
): Partial<JobData> {
  return {
    benefits: job.benefits ?? defaults.benefits,
    application_requirements:
      job.application_requirements ?? defaults.application_requirements,
    valid_through: job.valid_through ?? defaults.valid_through,
    job_identifier: job.job_identifier ?? defaults.job_identifier,
    job_source_name: job.job_source_name ?? defaults.job_source_name,
    department: job.department ?? defaults.department,
    travel_required: job.travel_required ?? defaults.travel_required,
    career_level: job.career_level ?? defaults.career_level,
    visa_sponsorship: job.visa_sponsorship ?? defaults.visa_sponsorship,
  };
}

/**
 * Normalizes skills and requirements fields
 */
function normalizeSkillsAndRequirements(
  job: Partial<JobData>,
  defaults: JobData
): Partial<JobData> {
  return {
    languages: job.languages ?? defaults.languages,
    skills: job.skills ?? defaults.skills,
    qualifications: job.qualifications ?? defaults.qualifications,
    education_requirements:
      job.education_requirements ?? defaults.education_requirements,
    experience_requirements:
      job.experience_requirements ?? defaults.experience_requirements,
    responsibilities: job.responsibilities ?? defaults.responsibilities,
  };
}

/**
 * Normalizes categorization fields
 */
function normalizeCategorizationInfo(
  job: Partial<JobData>,
  defaults: JobData
): Partial<JobData> {
  return {
    featured: job.featured ?? defaults.featured,
    industry: job.industry ?? defaults.industry,
    occupational_category:
      job.occupational_category ?? defaults.occupational_category,
  };
}

/**
 * Normalizes job data with proper defaults and validation
 * Broken down into smaller functions to reduce cognitive complexity
 */
function normalizeJobData(job: Partial<JobData>, sourceUrl: string): JobData {
  const defaults = createJobDataDefaults();

  return {
    ...defaults,
    ...normalizeBasicJobInfo(job, defaults, sourceUrl),
    ...normalizeSalaryInfo(job, defaults),
    ...normalizeWorkplaceInfo(job, defaults),
    ...normalizeJobMetadataInfo(job, defaults),
    ...normalizeSkillsAndRequirements(job, defaults),
    ...normalizeCategorizationInfo(job, defaults),
  };
}

async function processJobsBatch(jobs: PipelineJob[]): Promise<{
  results: ProcessingResult[];
  errors: ProcessingError[];
  duplicates: ProcessingDuplicate[];
}> {
  const results: ProcessingResult[] = [];
  const errors: ProcessingError[] = [];
  const duplicates: ProcessingDuplicate[] = [];

  // Process all jobs in parallel with Promise.all to avoid await-in-loop
  const jobPromises = jobs.map(async (job, index) => {
    const jobId = `job-${index + 1}`;

    try {
      logger.info(`🔄 Processing job ${index + 1}`, {
        sourceUrl: job.sourceUrl,
        source: job.source,
      });

      // Database-level deduplication is now handled during save operations
      // This simplifies the pipeline by removing pre-processing deduplication checks

      // Step 1: Extract job data using AI (only for non-duplicates)
      logger.debug('Starting AI extraction for job', {
        jobIndex: index + 1,
        sourceUrl: job.sourceUrl,
        contentLength: job.content.length,
        source: job.source,
        batchId: job.metadata?.batchId,
        memoryUsage: getMemoryUsage(),
      });

      const extractionResult = await extractJob({
        content: job.content,
        sourceUrl: job.sourceUrl,
        sourcedAt: new Date().toISOString(),
      });

      logger.debug('AI extraction completed for job', {
        jobIndex: index + 1,
        success: true,
        duration: extractionResult.metadata.duration,
        cost: extractionResult.metadata.cost,
        title: (extractionResult.job as { title?: string }).title,
        company: (extractionResult.job as { company?: string | null }).company,
      });

      // Track AI processing success
      logger.pipeline({
        step: 'PROCESSED',
        source: job.source || 'unknown',
        jobCount: 1,
        success: true,
        duration: extractionResult.metadata.duration,
        batchId: job.metadata?.batchId as string,
        metadata: {
          title: job.metadata?.title || 'Unknown Title',
          company: job.metadata?.company || 'Unknown Company',
          cost: extractionResult.metadata.cost,
          sourceUrl: job.sourceUrl,
        },
      });

      // Step 2: Normalize extracted job data and inject webhook source
      const extractedJobWithSource = {
        ...extractionResult.job,
        // Inject webhook source info - this is critical for database attribution
        source_name: job.source || null, // Data source (WeWorkRemotely, JobDataAPI, etc.)
        job_source_name:
          (
            extractionResult.aiSdkResult.object as {
              job_source_name?: string | null;
            }
          ).job_source_name ||
          (job.metadata as Record<string, unknown>)?.job_source_name ||
          null, // ATS/job board (extracted by AI)
      } as Partial<JobData>;

      const normalizedJob = normalizeJobData(
        extractedJobWithSource,
        job.sourceUrl
      );

      // Step 3: Apply tagging system (The Missing Layer!)
      const { job: taggedJob, tags } = await tagJob(normalizedJob);

      // Step 4: Check if this is an update to an existing job
      const existingJobId = (job.metadata as Record<string, unknown>)?.jobId as
        | string
        | undefined;

      let saveResult: { success: boolean; job?: unknown; error?: unknown };
      if (existingJobId) {
        // Update existing job with AI processing results
        const { updateJobWithAIProcessing } = await import('@/lib/storage');
        saveResult = await updateJobWithAIProcessing(
          existingJobId,
          taggedJob,
          extractionResult.metadata
        );
        logger.info(`🔄 Updating existing job ${existingJobId}`);
      } else {
        // Save new job to database with tags
        saveResult = await saveProcessedJob(
          taggedJob,
          extractionResult.metadata,
          tags
        );
      }

      if (!saveResult.success) {
        throw new Error(`Failed to save job to database: ${saveResult.error}`);
      }

      const databaseId = (existingJobId ||
        (saveResult.success && 'id' in saveResult
          ? saveResult.id
          : undefined)) as string | undefined;

      logger.info(`✅ Job ${index + 1} processed and saved successfully`, {
        cost: extractionResult.metadata.cost,
        duration: extractionResult.metadata.duration,
        databaseId,
        title: normalizedJob.title,
        company: normalizedJob.company,
        isUpdate: !!existingJobId,
      });

      // Track successful database storage
      logger.pipeline({
        step: 'STORED',
        source: job.source || 'unknown',
        jobCount: 1,
        success: true,
        batchId: job.metadata?.batchId as string,
        metadata: {
          title: normalizedJob.title,
          company: normalizedJob.company,
          databaseId,
          sourceUrl: job.sourceUrl,
          tags: tags?.length || 0,
        },
      });

      return {
        type: 'success' as const,
        result: {
          jobId: databaseId || jobId,
          status: 'success' as const,
          extractedJob: normalizedJob,
          metadata: extractionResult.metadata,
          savedToDatabase: true,
          databaseId: databaseId || null,
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';

      // Determine error type and stage
      const isAiExtractionError =
        errorMessage.includes('schema') ||
        errorMessage.includes('generate') ||
        errorMessage.includes('OpenAI') ||
        errorMessage.includes('AI SDK');
      const isStorageError = errorMessage.includes(
        'Failed to save job to database'
      );
      const isTaggingError =
        errorMessage.includes('tagging') || errorMessage.includes('tag');

      // Helper function to determine error type
      const getErrorType = () => {
        if (isAiExtractionError) {
          return 'ai_extraction';
        }
        if (isStorageError) {
          return 'database';
        }
        if (isTaggingError) {
          return 'tagging';
        }
        return 'unknown';
      };

      logger.critical(`❌ Job ${index + 1} processing failed`, {
        error: errorMessage,
        cause: error instanceof Error ? error.cause : undefined,
        stack:
          error instanceof Error
            ? error.stack?.split('\n').slice(0, 10)
            : undefined,
        sourceUrl: job.sourceUrl,
        jobIndex: index + 1,
        source: job.source,
        batchId: job.metadata?.batchId,
        contentLength: job.content.length,
        contentPreview: job.content.substring(0, 200),
        errorType: getErrorType(),
        memoryUsage: getMemoryUsage(),
        environment: process.env.NODE_ENV,
        nodeVersion: process.version,
      });

      // Track processing or storage failure
      logger.pipeline({
        step: isStorageError ? 'STORED' : 'PROCESSED',
        source: job.source || 'unknown',
        jobCount: 1,
        success: false,
        error: errorMessage,
        batchId: job.metadata?.batchId as string,
        metadata: {
          title: job.metadata?.title || 'Unknown Title',
          company: job.metadata?.company || 'Unknown Company',
          sourceUrl: job.sourceUrl,
          failureType: getErrorType(),
          errorType: getErrorType(),
        },
      });

      return {
        type: 'error' as const,
        error: {
          jobId,
          status: 'error' as const,
          error: errorMessage,
          sourceUrl: job.sourceUrl,
        },
      };
    }
  });

  // Wait for all jobs to complete
  const jobResults = await Promise.all(jobPromises);

  // Separate results and errors (duplicates now handled at database level)
  for (const result of jobResults) {
    if (result.type === 'success') {
      results.push(result.result);
    } else {
      errors.push(result.error);
    }
  }

  return { results, errors, duplicates };
}

/**
 * Parses and validates request body for pipeline jobs
 * Now accepts raw body string instead of NextRequest
 */
function parseRequestBody(rawBody: string): {
  success: boolean;
  response?: NextResponse;
  jobs?: PipelineJob[];
  isBatch?: boolean;
  batchId?: string;
} {
  let data: PipelineBatch | PipelineJob;

  try {
    data = JSON.parse(rawBody);
  } catch (error) {
    logger.alert(
      '❌ Pipeline received invalid JSON - potential webhook issue',
      {
        error: (error as Error).message,
        bodyPreview: rawBody.slice(0, 200),
        contentType: 'application/json',
      }
    );
    return {
      success: false,
      response: NextResponse.json(
        { error: 'Invalid JSON format' },
        { status: 400 }
      ),
    };
  }

  // Determine if this is a batch or single job
  const isBatch = 'jobs' in data;
  const jobs: PipelineJob[] = isBatch
    ? PipelineBatchSchema.parse(data).jobs
    : [PipelineJobSchema.parse(data)];

  return {
    success: true,
    jobs,
    isBatch,
    batchId: isBatch ? (data as PipelineBatch).batchId : undefined,
  };
}

/**
 * Handles failure rate alerts based on processing results
 * Extracted to reduce complexity of main handler
 */
function handleFailureRateAlerts(
  results: ProcessingResult[],
  errors: ProcessingError[],
  duplicates: ProcessingDuplicate[],
  jobs: PipelineJob[],
  processingTime: number
): void {
  const successCount = results.length;
  const errorCount = errors.length;
  const duplicateCount = duplicates.length;
  const failureRate = (errorCount / jobs.length) * 100;
  const duplicateRate = (duplicateCount / jobs.length) * 100;

  // Alert on high failure rates
  if (failureRate >= 50 && jobs.length >= 5) {
    logger.critical('🚨 High failure rate in pipeline processing', {
      failureRate: `${failureRate.toFixed(1)}%`,
      successful: successCount,
      failed: errorCount,
      duplicates: duplicateCount,
      total: jobs.length,
      errors: errors.slice(0, 3), // First 3 errors for analysis
    });
  } else if (failureRate >= 25 && jobs.length >= 10) {
    logger.alert('⚠️ Elevated failure rate in pipeline processing', {
      failureRate: `${failureRate.toFixed(1)}%`,
      successful: successCount,
      failed: errorCount,
      duplicates: duplicateCount,
      total: jobs.length,
    });
  }

  // Alert on complete failures
  if (successCount === 0 && jobs.length > 0) {
    logger.critical('🚨 Complete pipeline failure - no jobs processed', {
      total: jobs.length,
      duplicates: duplicateCount,
      errors: errors.slice(0, 5),
      processingTime,
    });
  }

  // Log duplicate rate information for monitoring
  if (duplicateRate >= 80 && duplicateCount >= 5) {
    logger.alert(
      '📊 High duplicate rate detected - may indicate data quality issues',
      {
        duplicateRate: `${duplicateRate.toFixed(1)}%`,
        duplicateCount,
        successCount,
        total: jobs.length,
        duplicateBreakdown: {
          level1: duplicates.filter((d) => d.duplicateInfo.layer === 'level1')
            .length,
          level2: duplicates.filter((d) => d.duplicateInfo.layer === 'level2')
            .length,
          level3: duplicates.filter((d) => d.duplicateInfo.layer === 'level3')
            .length,
        },
      }
    );
  } else if (duplicateCount > 0) {
    logger.info('💰 Cost savings from duplicate detection', {
      duplicateRate: `${duplicateRate.toFixed(1)}%`,
      duplicateCount,
      costSavings: `~${duplicateCount} AI calls saved`,
    });
  }
}

/**
 * Creates success response for pipeline processing
 * Extracted to reduce complexity of main handler
 */
function createSuccessResponse(
  results: ProcessingResult[],
  errors: ProcessingError[],
  duplicates: ProcessingDuplicate[],
  jobs: PipelineJob[],
  processingTime: number,
  batchId?: string
): NextResponse {
  const successCount = results.length;
  const savedCount = results.filter((r) => r.savedToDatabase).length;
  const errorCount = errors.length;
  const duplicateCount = duplicates.length;
  const totalCost = results.reduce(
    (sum, r) => sum + (r.metadata.cost?.total || 0),
    0
  );
  const failureRate = (errorCount / jobs.length) * 100;
  const duplicateRate = (duplicateCount / jobs.length) * 100;

  const response = {
    success: true,
    summary: {
      total: jobs.length,
      successful: successCount,
      savedToDatabase: savedCount,
      failed: errorCount,
      duplicates: duplicateCount,
      processingTime,
      totalCost,
      failureRate: `${failureRate.toFixed(1)}%`,
      duplicateRate: `${duplicateRate.toFixed(1)}%`,
      saveSuccessRate: `${((savedCount / jobs.length) * 100).toFixed(1)}%`,
      costSavings:
        duplicateCount > 0 ? `Saved ~${duplicateCount} AI calls` : undefined,
    },
    results,
    errors: errors.length > 0 ? errors : undefined,
    duplicates: duplicates.length > 0 ? duplicates : undefined,
    timestamp: new Date().toISOString(),
  };

  logger.info('🎯 Pipeline ingestion completed', {
    summary: response.summary,
    batchId,
    savedJobs: savedCount,
    duplicateJobs: duplicateCount,
    totalJobs: jobs.length,
    databaseIds: results.map((r) => r.databaseId).filter(Boolean),
    duplicateBreakdown:
      duplicateCount > 0
        ? {
            level1: duplicates.filter((d) => d.duplicateInfo.layer === 'level1')
              .length,
            level2: duplicates.filter((d) => d.duplicateInfo.layer === 'level2')
              .length,
            level3: duplicates.filter((d) => d.duplicateInfo.layer === 'level3')
              .length,
          }
        : undefined,
  });

  return NextResponse.json(response, {
    status: successCount > 0 ? 200 : 400,
    headers: {
      'X-Processing-Time': processingTime.toString(),
      'X-Success-Rate': ((successCount / jobs.length) * 100).toFixed(1),
    },
  });
}

/**
 * Main pipeline handler - with manual QStash signature verification
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    logger.info('🚀 Pipeline ingestion started', {
      method: request.method,
      url: request.url,
      timestamp: new Date().toISOString(),
    });

    // Initialize QStash receiver with environment variables inside function
    const currentSigningKey = process.env.QSTASH_CURRENT_SIGNING_KEY;
    const nextSigningKey = process.env.QSTASH_NEXT_SIGNING_KEY;

    if (!(currentSigningKey && nextSigningKey)) {
      logger.error('❌ Missing QStash signing keys', {
        hasCurrentKey: !!currentSigningKey,
        hasNextKey: !!nextSigningKey,
      });
      return NextResponse.json(
        { error: 'Missing signing keys' },
        { status: 500 }
      );
    }

    const receiver = new Receiver({
      currentSigningKey,
      nextSigningKey,
    });

    // Get signature from headers
    const signature = request.headers.get('Upstash-Signature');
    if (!signature) {
      logger.warn('⚠️ Missing QStash signature in pipeline ingestion');
      return NextResponse.json({ error: 'Missing signature' }, { status: 403 });
    }

    // Get the raw body text for signature verification
    const rawBody = await request.text();

    // Verify signature
    const isValid = await receiver.verify({
      body: rawBody,
      signature,
      url: request.url,
    });

    if (!isValid) {
      logger.warn('⚠️ Invalid QStash signature in pipeline ingestion');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 403 });
    }

    // Parse and validate request body
    const parseResult = await parseRequestBody(rawBody);
    if (!(parseResult.success && parseResult.jobs)) {
      return (
        parseResult.response ||
        NextResponse.json({ error: 'Parsing failed' }, { status: 400 })
      );
    }

    const { jobs, isBatch, batchId } = parseResult;

    logger.info(
      `📦 Processing ${isBatch ? 'batch of' : 'single'} ${jobs.length} job(s)`,
      {
        batchId,
        jobCount: jobs.length,
      }
    );

    // Process jobs with flow control
    const { results, errors, duplicates } = await processJobsBatch(jobs);

    // Handle failure rate alerts
    const processingTime = Date.now() - startTime;
    handleFailureRateAlerts(results, errors, duplicates, jobs, processingTime);

    // Create and return success response
    return createSuccessResponse(
      results,
      errors,
      duplicates,
      jobs,
      processingTime,
      batchId
    );
  } catch (error) {
    const processingTime = Date.now() - startTime;
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';

    logger.critical('💥 Critical pipeline ingestion failure', {
      error: errorMessage,
      processingTime,
      url: request.url,
      method: request.method,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        processingTime,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Allow testing without QStash verification in development
export const GET = (_request: NextRequest) => {
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Not allowed in production' },
      { status: 403 }
    );
  }

  return NextResponse.json({
    message: '🚀 Pipeline ingestion endpoint ready',
    version: 'v2.0.0',
    features: [
      '✅ QStash webhook verification',
      '✅ Batch job processing',
      '✅ AI extraction with GPT-4o-mini',
      '✅ Database persistence (FIXED!)',
      '✅ Job normalization & validation',
      '✅ Error handling & retry',
      '✅ Cost tracking',
      '✅ Processing metrics',
      '✅ Flow control support',
    ],
    usage: {
      single: 'POST with PipelineJob schema',
      batch: 'POST with PipelineBatch schema',
    },
    timestamp: new Date().toISOString(),
  });
};
