import { NextResponse } from 'next/server';
import { logger } from '@/lib/utils';

// Airtable schema types
interface AirtableField {
  id: string;
  name: string;
  type: string;
  options?: unknown;
}

interface AirtableTable {
  id: string;
  name: string;
  fields: AirtableField[];
}

interface AirtableBaseResponse {
  tables: AirtableTable[];
}

/**
 * GET /api/airtable-schema
 * Fetches the table schema from Airtable to see available fields
 * Note: This endpoint is deprecated as it relied on environment variables.
 * Use board-specific configuration instead.
 */
export async function GET() {
  try {
    return NextResponse.json(
      {
        error:
          'This endpoint is deprecated. Airtable schema should be fetched using board-specific configuration.',
        help: 'Use the connection test endpoint with a specific boardId instead.',
      },
      { status: 410 }
    );
  } catch (error) {
    logger.error('Error fetching Airtable schema:', error);
    return NextResponse.json(
      { error: 'Failed to fetch schema', details: error },
      { status: 500 }
    );
  }
}
