import { createClient as createAdminClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';
import { updateAirtableRecordInfo } from '@/lib/job-board-service';
import { getAirtablePat } from '@/lib/secrets-manager';
import { logger } from '@/lib/utils';
import {
  validateAirtableBaseId,
  validateAirtablePat,
  validateAirtableTableName,
} from '@/lib/validation';

// Regex patterns defined at top level for performance
const HTTP_ERROR_REGEX = /HTTP (\d+): (.+)/;

// Admin client with service role for bypassing RLS
const supabaseAdmin = createAdminClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

interface AirtableConfig {
  pat: string;
  baseId: string;
  tableName: string;
}

interface TestConnectionRequest {
  boardId?: string;
  airtableConfig?: {
    pat: string;
    baseId: string;
    tableName: string;
  };
}

interface TableInfo {
  fields?: { name: string }[];
}

interface AirtableHeaders {
  Authorization: string;
  'Content-Type': string;
  [key: string]: string;
}

async function getConfigFromBoard(
  boardId: string
): Promise<AirtableConfig | null> {
  const pat = await getAirtablePat(boardId);

  // Use admin client to bypass RLS when reading job board configs
  const { data: board } = await supabaseAdmin
    .from('job_board_configs')
    .select('airtable_base_id, airtable_table_name')
    .eq('id', boardId)
    .single();

  if (!(board && pat)) {
    return null;
  }

  return {
    pat,
    baseId: board.airtable_base_id,
    tableName: board.airtable_table_name,
  };
}

function createAirtableHeaders(pat: string): AirtableHeaders {
  return {
    Authorization: `Bearer ${pat}`,
    'Content-Type': 'application/json',
  };
}

async function fetchTableSchema(
  baseId: string,
  tableName: string,
  headers: AirtableHeaders
) {
  const schemaResponse = await fetch(
    `https://api.airtable.com/v0/meta/bases/${baseId}/tables`,
    { headers }
  );

  let tableInfo: TableInfo | undefined;
  let hasTable = false;

  if (schemaResponse.ok) {
    const schemaData = await schemaResponse.json();
    const tables = schemaData.tables || [];
    tableInfo = tables.find(
      (t: { name: string; fields?: { name: string }[] }) => t.name === tableName
    );
    hasTable = !!tableInfo;
  }

  return { tableInfo, hasTable };
}

async function fetchSinglePage(
  baseId: string,
  tableName: string,
  headers: AirtableHeaders,
  offset?: string
) {
  const url = new URL(
    `https://api.airtable.com/v0/${baseId}/${encodeURIComponent(tableName)}`
  );
  // Don't set maxRecords - let Airtable use its default pagination
  // Don't specify fields to get all records - field filtering can cause issues
  // if the specified field doesn't exist in all records

  // Don't use any view - this should get all records from the table
  // Note: If tableName is actually a view name, this might be the issue

  if (offset) {
    url.searchParams.set('offset', offset);
  }

  const response = await fetch(url.toString(), { headers });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
  }

  return response.json();
}

async function countAllRecords(
  baseId: string,
  tableName: string,
  headers: AirtableHeaders
) {
  const firstPageData = await fetchSinglePage(baseId, tableName, headers);
  const records = firstPageData.records || [];
  let totalCount = records.length;
  let currentOffset = firstPageData.offset;

  // Continue fetching to get accurate count (limit to 50 pages to avoid timeouts)
  let pageCount = 1;
  const maxPages = 50; // Limits to ~5000 records max for performance

  // Fetch pages sequentially to get accurate count
  while (currentOffset && pageCount < maxPages) {
    try {
      const pageData = await fetchSinglePage(
        baseId,
        tableName,
        headers,
        currentOffset
      );
      const pageRecords = pageData.records || [];
      totalCount += pageRecords.length;

      currentOffset = pageData.offset; // Update offset for next iteration
      pageCount++;

      // If we got fewer than 100 records, we've reached the end
      if (pageRecords.length < 100) {
        break;
      }
    } catch (error) {
      // If we hit an error, stop counting but don't fail the whole test
      logger.warn(`Error fetching page ${pageCount}:`, error);
      break;
    }
  }

  const actualTotal =
    pageCount >= maxPages ? `${totalCount}+` : totalCount.toString();
  const countStatus =
    pageCount >= maxPages
      ? `(showing first ${totalCount}, likely more)`
      : '(accessible via API in default view)';

  return { actualTotal, countStatus, firstPageData };
}

/**
 * Test write permissions by creating and immediately deleting a test record
 */
async function testWritePermissions(
  baseId: string,
  tableName: string,
  headers: AirtableHeaders
): Promise<{ canWrite: boolean; error?: string }> {
  try {
    // First, get the table schema to find available fields
    const { tableInfo } = await fetchTableSchema(baseId, tableName, headers);

    if (!tableInfo?.fields || tableInfo.fields.length === 0) {
      return {
        canWrite: false,
        error: 'No fields found in table for write test',
      };
    }

    // Use the first available field for the test
    const firstField = tableInfo.fields[0];
    const testRecord = {
      records: [
        {
          fields: {
            [firstField.name]: 'Connection Test - Safe to Delete',
          },
        },
      ],
    };

    const createResponse = await fetch(
      `https://api.airtable.com/v0/${baseId}/${encodeURIComponent(tableName)}`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify(testRecord),
      }
    );

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      return {
        canWrite: false,
        error: `Create failed: ${createResponse.status} ${errorText}`,
      };
    }

    const createResult = await createResponse.json();
    const recordId = createResult.records?.[0]?.id;

    if (recordId) {
      // Immediately delete the test record
      const deleteResponse = await fetch(
        `https://api.airtable.com/v0/${baseId}/${encodeURIComponent(
          tableName
        )}/${recordId}`,
        {
          method: 'DELETE',
          headers,
        }
      );

      // Don't fail if delete fails - the important part is that we could create
      if (!deleteResponse.ok) {
        logger.warn('Test record created but could not be deleted:', recordId);
      }
    }

    return { canWrite: true };
  } catch (error) {
    return {
      canWrite: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

async function getAirtableConfig(
  boardId?: string,
  airtableConfig?: AirtableConfig
): Promise<AirtableConfig | null> {
  if (boardId) {
    return await getConfigFromBoard(boardId);
  }

  if (airtableConfig) {
    return {
      pat: airtableConfig.pat,
      baseId: airtableConfig.baseId,
      tableName: airtableConfig.tableName,
    };
  }

  // No fallbacks to environment variables - require explicit configuration
  return null;
}

function validateConfig(
  config: AirtableConfig | null
): config is AirtableConfig {
  if (!(config?.pat && config?.baseId && config?.tableName)) {
    return false;
  }

  // Validate Airtable base ID format
  if (!validateAirtableBaseId(config.baseId)) {
    return false;
  }

  // Validate PAT format
  if (!validateAirtablePat(config.pat)) {
    return false;
  }

  // Validate table name
  if (!validateAirtableTableName(config.tableName)) {
    return false;
  }

  return true;
}

async function testAirtableConnection(config: AirtableConfig) {
  const { pat, baseId, tableName } = config;
  const headers = createAirtableHeaders(pat);
  const startTime = Date.now();

  try {
    // Get table schema, count records, and test write permissions in parallel
    const [{ tableInfo, hasTable }, { actualTotal, countStatus }, writeTest] =
      await Promise.all([
        fetchTableSchema(baseId, tableName, headers),
        countAllRecords(baseId, tableName, headers),
        testWritePermissions(baseId, tableName, headers),
      ]);

    const responseTime = Date.now() - startTime;

    // If table doesn't exist, connection is invalid
    if (!hasTable) {
      return {
        success: false,
        error: `Table "${tableName}" not found in base`,
        details: 'The specified table does not exist in this Airtable base',
        config: {
          baseId,
          tableName,
          hasPat: !!pat,
        },
        responseTime,
      };
    }

    return {
      success: true,
      message: '✅ Connection successful!',
      config: {
        baseId,
        tableName,
        hasPat: true,
      },
      tableInfo: {
        exists: hasTable,
        name: tableName,
        fields: tableInfo?.fields?.length || 'unknown',
        fieldNames:
          tableInfo?.fields
            ?.map((f: { name: string }) => f.name)
            .slice(0, 10) || [],
      },
      records: {
        totalCount: actualTotal,
        countStatus,
      },
      permissions: {
        read: true,
        write: writeTest.canWrite,
        writeError: writeTest.error,
      },
      performance: {
        responseTime: `${responseTime}ms`,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (networkError) {
    const responseTime = Date.now() - startTime;

    if (
      networkError instanceof Error &&
      networkError.message.includes('HTTP')
    ) {
      // Parse HTTP error
      const [, status, errorText] =
        networkError.message.match(HTTP_ERROR_REGEX) || [];
      let errorDetails: Record<string, unknown> = {};

      try {
        errorDetails = JSON.parse(errorText);
      } catch {
        errorDetails = { rawError: errorText };
      }

      return {
        success: false,
        error: `Airtable API Error (${status})`,
        details: errorDetails,
        config: {
          baseId,
          tableName,
          hasPat: !!pat,
        },
        responseTime,
      };
    }

    return {
      success: false,
      error: 'Network error connecting to Airtable',
      details:
        networkError instanceof Error
          ? networkError.message
          : 'Unknown network error',
      config: {
        baseId,
        tableName,
        hasPat: !!pat,
      },
      responseTime,
    };
  }
}

export async function POST(request: Request) {
  try {
    const { boardId, airtableConfig }: TestConnectionRequest =
      await request.json();

    if (!(boardId || airtableConfig)) {
      return NextResponse.json(
        { error: 'Either boardId or airtableConfig is required' },
        { status: 400 }
      );
    }

    const config = await getAirtableConfig(boardId, airtableConfig);

    if (boardId && !config) {
      return NextResponse.json(
        { error: `Board not found or incomplete configuration: ${boardId}` },
        { status: 404 }
      );
    }

    if (!validateConfig(config)) {
      const configToCheck = config as Partial<AirtableConfig> | null;
      const validationErrors: string[] = [];

      if (!configToCheck?.pat) {
        validationErrors.push('PAT token is required');
      } else if (!validateAirtablePat(configToCheck.pat)) {
        validationErrors.push(
          'PAT token format is invalid (must start with "pat" and be 40+ characters)'
        );
      }

      if (!configToCheck?.baseId) {
        validationErrors.push('Base ID is required');
      } else if (!validateAirtableBaseId(configToCheck.baseId)) {
        validationErrors.push(
          'Base ID format is invalid (must be "app" + 14 alphanumeric characters)'
        );
      }

      if (!configToCheck?.tableName) {
        validationErrors.push('Table name is required');
      } else if (!validateAirtableTableName(configToCheck.tableName)) {
        validationErrors.push(
          'Table name is invalid (must be 1-100 characters)'
        );
      }

      return NextResponse.json(
        {
          error: 'Invalid Airtable configuration',
          validationErrors,
          examples: {
            baseId: 'apprhCjWTxfG3JX5p',
            pat: 'patXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
            tableName: 'Jobs',
          },
        },
        { status: 400 }
      );
    }

    const result = await testAirtableConnection(config);

    // If test was successful and we have a boardId, update the record count in database
    if (result.success && boardId && result.records?.totalCount) {
      const recordCount = Number.parseInt(
        result.records.totalCount.toString().replace('+', ''),
        10
      );
      await updateAirtableRecordInfo(boardId, recordCount);
    }

    return NextResponse.json(result);
  } catch (error) {
    logger.error('Airtable test connection error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
