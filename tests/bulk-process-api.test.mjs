#!/usr/bin/env node

/**
 * Test script for the new bulk process API endpoint
 *
 * Tests the /api/jobs POST endpoint with action: "bulkProcessNow"
 */

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001';

/**
 * Test Results Tracker
 */
const results = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: [],
};

function logTest(name, passed, details = '') {
  results.total++;
  if (details) {
  }

  if (passed) {
    results.passed++;
  } else {
    results.failed++;
    results.errors.push({ test: name, details });
  }
}

/**
 * Test 1: Bulk Process API Endpoint Validation
 */
async function testBulkProcessValidation() {
  // Test 1.1: Missing IDs array
  try {
    const response = await fetch(`${BASE_URL}/api/jobs`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'bulkProcessNow',
        // Missing ids array
      }),
    });

    const data = await response.json();
    const isCorrectError =
      response.status === 400 && data.error === 'ids array is required';

    logTest(
      'Missing IDs validation',
      isCorrectError,
      `Status: ${response.status}, Error: ${data.error}`
    );
  } catch (error) {
    logTest('Missing IDs validation', false, `Error: ${error.message}`);
  }

  // Test 1.2: Empty IDs array
  try {
    const response = await fetch(`${BASE_URL}/api/jobs`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'bulkProcessNow',
        ids: [],
      }),
    });

    const data = await response.json();
    const isCorrectError =
      response.status === 400 && data.error === 'ids array is required';

    logTest(
      'Empty IDs array validation',
      isCorrectError,
      `Status: ${response.status}, Error: ${data.error}`
    );
  } catch (error) {
    logTest('Empty IDs array validation', false, `Error: ${error.message}`);
  }

  // Test 1.3: Invalid UUID format
  try {
    const response = await fetch(`${BASE_URL}/api/jobs`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'bulkProcessNow',
        ids: ['non-existent-id-1', 'non-existent-id-2'],
      }),
    });

    const data = await response.json();
    const isCorrectError =
      response.status === 400 && data.error.includes('Invalid UUID format');

    logTest(
      'Invalid UUID format validation',
      isCorrectError,
      `Status: ${response.status}, Error: ${data.error}`
    );
  } catch (error) {
    logTest('Invalid UUID format validation', false, `Error: ${error.message}`);
  }

  // Test 1.4: Non-existent but valid UUID job IDs
  try {
    const response = await fetch(`${BASE_URL}/api/jobs`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'bulkProcessNow',
        ids: [
          '550e8400-e29b-41d4-a716-************',
          '550e8400-e29b-41d4-a716-************',
        ],
      }),
    });

    const data = await response.json();
    const isCorrectError =
      response.status === 404 &&
      data.error === 'No jobs found with provided IDs';

    logTest(
      'Non-existent job IDs validation',
      isCorrectError,
      `Status: ${response.status}, Error: ${data.error}, Details: ${
        data.details || 'none'
      }`
    );
  } catch (error) {
    logTest(
      'Non-existent job IDs validation',
      false,
      `Error: ${error.message}`
    );
  }
}

/**
 * Test 2: API Response Structure
 */
async function testResponseStructure() {
  // First, let's get some actual job IDs from the database
  try {
    const jobsResponse = await fetch(`${BASE_URL}/api/jobs?limit=2`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });

    if (!jobsResponse.ok) {
      logTest(
        'Get jobs for testing',
        false,
        `Failed to fetch jobs: ${jobsResponse.status}`
      );
      return;
    }

    const jobsData = await jobsResponse.json();
    const jobIds = jobsData.jobs?.slice(0, 1).map((job) => job.id) || [];

    if (jobIds.length === 0) {
      logTest('Get jobs for testing', false, 'No jobs found in database');
      return;
    }

    logTest(
      'Get jobs for testing',
      true,
      `Found ${jobIds.length} job(s) to test with`
    );

    // Test the bulk process endpoint with real job IDs
    const response = await fetch(`${BASE_URL}/api/jobs`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'bulkProcessNow',
        ids: jobIds,
      }),
    });

    const data = await response.json();

    // Check response structure
    const hasCorrectStructure =
      data.success !== undefined &&
      data.message !== undefined &&
      data.stats !== undefined &&
      data.stats.total !== undefined &&
      data.stats.successful !== undefined &&
      data.stats.failed !== undefined;

    logTest(
      'Response structure validation',
      hasCorrectStructure,
      `Status: ${
        response.status
      }, Has stats: ${!!data.stats}, Message: ${data.message?.substring(
        0,
        50
      )}...`
    );

    // Check if processing was attempted
    const processingAttempted = data.stats.total === jobIds.length;

    logTest(
      'Processing attempt validation',
      processingAttempted,
      `Expected: ${jobIds.length}, Actual: ${data.stats.total}`
    );
  } catch (error) {
    logTest('Response structure test', false, `Error: ${error.message}`);
  }
}

/**
 * Print test summary
 */
function printSummary() {
  if (results.errors.length > 0) {
    results.errors.forEach((_error) => {});
  }
}

/**
 * Main Test Runner
 */
async function runAllTests() {
  try {
    await testBulkProcessValidation();
    await testResponseStructure();

    printSummary();

    // Exit with error code if tests failed
    process.exit(results.failed > 0 ? 1 : 0);
  } catch (_error) {
    process.exit(1);
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests();
}

export { runAllTests };
