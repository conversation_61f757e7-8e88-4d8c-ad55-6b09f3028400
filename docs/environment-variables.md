# Environment Variables Setup

## Quick Start

```bash
pnpm run env:pull # Pull all secrets from Vercel to .env.local

pnpm dev # Start development at http://localhost:3000
```

## Required Secrets (Store in Vercel)

### Crypto Keys (Generate Once)

```bash
# Encryption key for Airtable PAT storage (32 bytes)
node -e "console.log('SECRETS_ENCRYPTION_KEY=' + require('crypto').randomBytes(32).toString('hex'))"

# API endpoint protection keys (16 bytes each)
node -e "console.log('MONITOR_SECRET=' + require('crypto').randomBytes(16).toString('hex'))"
node -e "console.log('SCHEDULER_SECRET=' + require('crypto').randomBytes(16).toString('hex'))"
node -e "console.log('ADMIN_SECRET_KEY=' + require('crypto').randomBytes(16).toString('hex'))"
```

### Core Services

```env
OPENAI_API_KEY=sk-proj-your-openai-key
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
NEXT_PUBLIC_BASE_URL=https://bordfeed.com
```

### Apify Integration

```env
APIFY_TOKEN=your-apify-api-token
APIFY_BASE_URL=https://api.apify.com/v2
JOBDATAAPI_ACTOR_ID=your-jobdataapi-actor-id
WORKABLE_ACTOR_ID=your-workable-actor-id
WWR_ACTOR_ID=your-wwr-actor-id
```

### Upstash (QStash)

```env
QSTASH_TOKEN=your-qstash-token
QSTASH_CURRENT_SIGNING_KEY=your-qstash-signing-key
QSTASH_NEXT_SIGNING_KEY=your-qstash-next-signing-key
QSTASH_URL=https://qstash.upstash.io
```

### Monitoring & Notifications

```env
SLACK_WEBHOOK_URL=your-slack-webhook
NEXT_PUBLIC_MONITOR_SECRET=your-monitor-public-key
```

## Optional Integrations

```env
JOBDATA_API_KEY=YOUR_API_KEY
# Note: Airtable credentials are now configured per job board in the database
# AIRTABLE_PAT, AIRTABLE_BASE_ID, AIRTABLE_TABLE_NAME are no longer used
```

## Deduplication Configuration (All have sensible defaults)

```env
DEDUP_ENABLED=true
DEDUP_DEBUG=false
DEDUP_LEVEL1_ENABLED=true
DEDUP_LEVEL2_ENABLED=true
DEDUP_LEVEL3_ENABLED=true
DEDUP_LEVEL1_TTL_DAYS=30
DEDUP_LEVEL2_TTL_DAYS=30
DEDUP_LEVEL3_TTL_DAYS=7
```

## Auto-Generated (Vercel provides these)

```env
VERCEL_OIDC_TOKEN=auto-generated-by-vercel
```

## Setup Steps

1. **Generate crypto keys** (if not done already) using commands above
2. **Add all variables** to [Vercel Dashboard](https://vercel.com/dashboard) → Project Settings → Environment Variables
3. **Set for all environments** (Production, Preview, Development)
4. **Pull locally**: `pnpm run env:pull`
5. **Test**: `pnpm dev`
6. **Deploy**: `vercel --prod`

## Notes

- Secrets are stored in Vercel by default, not in `.env` files
- Use `pnpm run env:pull` to sync Vercel secrets to `.env.local`
- Crypto keys only need to be generated once per deployment
- Deduplication settings have sensible defaults and rarely need adjustment
- `VERCEL_OIDC_TOKEN` is automatically generated by Vercel CLI
- Optional variables can be added later as needed
