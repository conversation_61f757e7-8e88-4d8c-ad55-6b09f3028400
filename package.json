{"name": "bordfeed", "version": "0.1.7", "description": "AI-powered job board automation platform with intelligent extraction, monitoring, and multi-platform publishing", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "npx ultracite lint", "format": "npx ultracite format", "test": "npx tsx tests/monitor-utils.test.ts", "test:e2e": "npx playwright test", "env:pull": "vercel env pull", "env:push": "vercel env push", "deploy": "vercel --prod"}, "dependencies": {"@ai-sdk/openai": "2.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "@tanstack/react-table": "^8.21.3", "@upstash/qstash": "^2.8.2", "ai": "5.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "lucide-react": "^0.536.0", "next": "15.4.5", "openai": "^5.11.0", "react": "^19.1.1", "react-dom": "^19.1.1", "recharts": "^3.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "vercel": "^44.6.6", "zod": "^4.0.14"}, "devDependencies": {"@biomejs/biome": "2.1.3", "@playwright/test": "^1.54.2", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^22.17.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "ultracite": "5.1.2"}}