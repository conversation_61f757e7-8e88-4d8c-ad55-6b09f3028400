'use client';

import {
  type ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  type SortingState,
  useReactTable,
  type VisibilityState,
} from '@tanstack/react-table';
import { ChevronDown, Code, Filter, Play, Plus, Trash2, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CAREER_LEVELS } from '@/lib/career-levels';
import { countries } from '@/lib/data/countries';
import { CURRENCY_CODES } from '@/lib/data/currencies';
import { LANGUAGE_CODES } from '@/lib/data/languages';
import { useBulkOperations } from '@/lib/hooks/use-bulk-operations';
import { useJobFilters } from '@/lib/hooks/use-job-filters';
import { useJobsApi } from '@/lib/hooks/use-jobs-api';
import { JOB_TYPES } from '@/lib/job-types';
import { WORKPLACE_TYPES } from '@/lib/workplace';
import { createColumns } from './columns';
import { DataTablePagination } from './data-table-pagination';
import { DataTableViewOptions } from './data-table-view-options';

// Create type for job filters to avoid any
type JobFilters = ReturnType<typeof useJobFilters>['filters'];

// Filter options
const statusOptions = [
  { value: 'all', label: 'All Statuses' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'filled', label: 'Filled' },
  { value: 'expired', label: 'Expired' },
  { value: 'cancelled', label: 'Cancelled' },
  { value: 'draft', label: 'Draft' },
];

const processingStatusOptions = [
  { value: 'all', label: 'All Processing' },
  { value: 'pending', label: 'Pending' },
  { value: 'completed', label: 'Completed' },
  { value: 'failed', label: 'Failed' },
];

const sourceTypeOptions = [
  { value: 'all', label: 'All Sources' },
  { value: 'workable', label: 'Workable' },
  { value: 'wwr_rss', label: 'WeWorkRemotely RSS' },
  { value: 'jobdata_api', label: 'JobData API' },
];

interface JobsStatsProps {
  pagination: {
    totalItems: number;
  };
  jobs: unknown[];
  executionTime?: number;
  getActiveFilterCount: () => number;
}

function JobsStats({
  pagination,
  jobs,
  executionTime,
  getActiveFilterCount,
}: JobsStatsProps) {
  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Total Jobs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">{pagination.totalItems}</div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Showing</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">{jobs.length}</div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Query Time</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">
            {executionTime ? `${executionTime}ms` : '—'}
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Active Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">{getActiveFilterCount()}</div>
        </CardContent>
      </Card>
    </div>
  );
}

interface JobsFilterSidebarProps {
  loading: boolean;
  filters: JobFilters;
  keywordInput: string;
  excludeKeywordInput: string;
  searchInput: string;
  setKeywordInput: (value: string) => void;
  setExcludeKeywordInput: (value: string) => void;
  updateFilter: (
    key: keyof JobFilters,
    value: string | string[] | number | undefined
  ) => void;
  updateSearchInput: (value: string) => void;
  toggleArrayFilter: (key: keyof JobFilters, item: string) => void;
  addIncludeKeyword: () => void;
  addExcludeKeyword: () => void;
  removeKeyword: (
    type: 'includeKeywords' | 'excludeKeywords',
    keyword: string
  ) => void;
  clearAllFilters: () => void;
  hasAnyFilters: () => boolean;
}

function JobsFilterSidebar({
  loading,
  filters,
  keywordInput,
  excludeKeywordInput,
  searchInput,
  setKeywordInput,
  setExcludeKeywordInput,
  updateFilter,
  updateSearchInput,
  toggleArrayFilter,
  addIncludeKeyword,
  addExcludeKeyword,
  removeKeyword,
  clearAllFilters,
  hasAnyFilters,
}: JobsFilterSidebarProps) {
  return (
    <div className="space-y-5 lg:col-span-1">
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
            {hasAnyFilters() && (
              <Button
                disabled={loading}
                onClick={clearAllFilters}
                size="sm"
                variant="ghost"
              >
                Clear All
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Search */}
          <div>
            <Label htmlFor="search">Search</Label>
            <Input
              disabled={loading}
              id="search"
              onChange={(e) => updateSearchInput(e.target.value)}
              placeholder="Search title, company, description..."
              value={searchInput}
            />
          </div>

          {/* Status */}
          <div>
            <Label>Status</Label>
            <Select
              disabled={loading}
              onValueChange={(value) =>
                updateFilter('status', value === 'all' ? undefined : value)
              }
              value={filters.status || 'all'}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Processing Status */}
          <div>
            <Label>Processing Status</Label>
            <Select
              disabled={loading}
              onValueChange={(value) =>
                updateFilter(
                  'processingStatus',
                  value === 'all' ? undefined : value
                )
              }
              value={filters.processingStatus || 'all'}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {processingStatusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Source Type */}
          <div>
            <Label>Source Type</Label>
            <Select
              disabled={loading}
              onValueChange={(value) =>
                updateFilter('sourceType', value === 'all' ? undefined : value)
              }
              value={filters.sourceType || 'all'}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {sourceTypeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Job Types */}
          <FilterSection
            disabled={loading}
            items={JOB_TYPES}
            onToggle={(item) => toggleArrayFilter('jobTypes', item)}
            selectedItems={filters.jobTypes || []}
            title="Job Types"
          />

          {/* Workplace Types */}
          <FilterSection
            disabled={loading}
            items={WORKPLACE_TYPES}
            onToggle={(item) => toggleArrayFilter('workplaceTypes', item)}
            selectedItems={filters.workplaceTypes || []}
            title="Workplace Types"
          />

          {/* Career Levels */}
          <FilterSection
            disabled={loading}
            items={CAREER_LEVELS}
            onToggle={(item) => toggleArrayFilter('careerLevels', item)}
            selectedItems={filters.careerLevels || []}
            title="Career Levels"
          />

          <Separator />

          {/* Salary Range */}
          <div>
            <Label>Salary Range</Label>
            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  disabled={loading}
                  onChange={(e) =>
                    updateFilter(
                      'salaryMin',
                      e.target.value ? Number(e.target.value) : undefined
                    )
                  }
                  placeholder="Min"
                  type="number"
                  value={filters.salaryMin || ''}
                />
                <Input
                  disabled={loading}
                  onChange={(e) =>
                    updateFilter(
                      'salaryMax',
                      e.target.value ? Number(e.target.value) : undefined
                    )
                  }
                  placeholder="Max"
                  type="number"
                  value={filters.salaryMax || ''}
                />
              </div>
              <FilterSection
                disabled={loading}
                items={CURRENCY_CODES}
                maxHeight="150px"
                onToggle={(item) => toggleArrayFilter('salaryCurrencies', item)}
                selectedItems={filters.salaryCurrencies || []}
                title="Currencies"
              />
            </div>
          </div>

          <Separator />

          <KeywordManagement
            addExcludeKeyword={addExcludeKeyword}
            addIncludeKeyword={addIncludeKeyword}
            excludeKeywordInput={excludeKeywordInput}
            filters={filters}
            keywordInput={keywordInput}
            loading={loading}
            removeKeyword={removeKeyword}
            setExcludeKeywordInput={setExcludeKeywordInput}
            setKeywordInput={setKeywordInput}
          />

          <Separator />

          {/* Countries */}
          <FilterSection
            disabled={loading}
            items={countries}
            maxHeight="200px"
            onToggle={(item) => toggleArrayFilter('countries', item)}
            selectedItems={filters.countries || []}
            title="Countries"
          />

          {/* Languages */}
          <FilterSection
            disabled={loading}
            items={LANGUAGE_CODES}
            maxHeight="150px"
            onToggle={(item) => toggleArrayFilter('languages', item)}
            selectedItems={filters.languages || []}
            title="Languages"
          />
        </CardContent>
      </Card>
    </div>
  );
}

interface KeywordManagementProps {
  loading: boolean;
  filters: JobFilters;
  keywordInput: string;
  excludeKeywordInput: string;
  setKeywordInput: (value: string) => void;
  setExcludeKeywordInput: (value: string) => void;
  addIncludeKeyword: () => void;
  addExcludeKeyword: () => void;
  removeKeyword: (
    type: 'includeKeywords' | 'excludeKeywords',
    keyword: string
  ) => void;
}

function KeywordManagement({
  loading,
  filters,
  keywordInput,
  excludeKeywordInput,
  setKeywordInput,
  setExcludeKeywordInput,
  addIncludeKeyword,
  addExcludeKeyword,
  removeKeyword,
}: KeywordManagementProps) {
  return (
    <>
      {/* Include Keywords */}
      <div>
        <Label>Include Keywords</Label>
        <div className="space-y-2">
          <div className="flex gap-2">
            <Input
              disabled={loading}
              onChange={(e) => setKeywordInput(e.target.value)}
              placeholder="Add keywords (comma-separated)"
              value={keywordInput}
            />
            <Button
              disabled={loading || !keywordInput.trim()}
              onClick={addIncludeKeyword}
              size="sm"
              type="button"
            >
              Add
            </Button>
          </div>
          <KeywordList
            keywords={filters.includeKeywords || []}
            onRemove={(keyword) => removeKeyword('includeKeywords', keyword)}
          />
        </div>
      </div>

      {/* Exclude Keywords */}
      <div>
        <Label>Exclude Keywords</Label>
        <div className="space-y-2">
          <div className="flex gap-2">
            <Input
              disabled={loading}
              onChange={(e) => setExcludeKeywordInput(e.target.value)}
              placeholder="Add keywords (comma-separated)"
              value={excludeKeywordInput}
            />
            <Button
              disabled={loading || !excludeKeywordInput.trim()}
              onClick={addExcludeKeyword}
              size="sm"
              type="button"
            >
              Add
            </Button>
          </div>
          <KeywordList
            keywords={filters.excludeKeywords || []}
            onRemove={(keyword) => removeKeyword('excludeKeywords', keyword)}
            variant="destructive"
          />
        </div>
      </div>
    </>
  );
}

export function JobsTable() {
  const router = useRouter();
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'created_at', desc: true },
  ]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    // Show most important columns by default
    title: true,
    company: true,
    status: true,
    processing_status: true,
    workplace_type: true,
    salary_min: true,
    created_at: true,
    // Hide detailed fields by default (can be toggled via View button)
    department: false,
    industry: false,
    occupational_category: false,
    monitor_status: false,
    featured: false,
    remote_region: false,
    workplace_city: false,
    workplace_country: false,
    timezone_requirements: false,
    travel_required: false,
    salary_currency: false,
    salary_unit: false,
    career_level: false,
    visa_sponsorship: false,
    languages: false,
    education_requirements: false,
    experience_requirements: false,
    skills: false,
    apply_method: false,
    posted_date: false,
    valid_through: false,
    job_source_name: false,
    job_identifier: false,
    updated_at: false,
    last_checked_at: false,
    monitor_attempts: false,
  });
  const [rowSelection, setRowSelection] = useState({});

  // Page size state
  const [pageSize, setPageSize] = useState(20);

  // Enhanced filter states from custom hook
  const {
    filters,
    keywordInput,
    excludeKeywordInput,
    searchInput,
    setKeywordInput,
    setExcludeKeywordInput,
    updateFilter,
    updateSearchInput,
    toggleArrayFilter,
    addIncludeKeyword,
    addExcludeKeyword,
    removeKeyword,
    clearAllFilters,
    getActiveFilterCount,
    hasAnyFilters,
  } = useJobFilters();

  // API and data management from custom hook
  const {
    jobs,
    loading,
    pagination,
    sqlQuery,
    executionTime,
    loadJobs,
    handlePageChange,
    handlePageSizeChange,
  } = useJobsApi({ filters, sorting, pageSize });

  // Handle page size changes
  const onPageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    handlePageSizeChange(newPageSize);
  };

  // Create columns with refresh callback
  const columns = createColumns(() => loadJobs());

  // Server-side table configuration
  const table = useReactTable({
    data: jobs,
    columns,
    pageCount: pagination.totalPages,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination: {
        pageIndex: pagination.currentPage - 1,
        pageSize: pagination.itemsPerPage,
      },
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
  });

  const selectedRows = table.getFilteredSelectedRowModel().rows;

  // Bulk operations hook
  const {
    handleBulkDelete,
    handleBulkStatusUpdate,
    handleBulkRequeueAI,
    handleBulkAirtablePush,
    handleBulkResetMonitor,
    handleBulkProcessNow,
  } = useBulkOperations({
    selectedRows,
    onRefresh: () => loadJobs(),
    onClearSelection: () => setRowSelection({}),
  });

  if (loading && jobs.length === 0) {
    return (
      <div className="flex items-center justify-center py-10">
        <div className="text-muted-foreground">Loading jobs...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <JobsStats
        executionTime={executionTime}
        getActiveFilterCount={getActiveFilterCount}
        jobs={jobs}
        pagination={pagination}
      />

      {/* Main Interface */}
      <Tabs className="space-y-6" defaultValue="jobs">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="jobs">Jobs & Filters</TabsTrigger>
          <TabsTrigger value="sql">SQL Query</TabsTrigger>
        </TabsList>

        <TabsContent className="space-y-6" value="jobs">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
            <JobsFilterSidebar
              addExcludeKeyword={addExcludeKeyword}
              addIncludeKeyword={addIncludeKeyword}
              clearAllFilters={clearAllFilters}
              excludeKeywordInput={excludeKeywordInput}
              filters={filters}
              hasAnyFilters={hasAnyFilters}
              keywordInput={keywordInput}
              loading={loading}
              removeKeyword={removeKeyword}
              searchInput={searchInput}
              setExcludeKeywordInput={setExcludeKeywordInput}
              setKeywordInput={setKeywordInput}
              toggleArrayFilter={toggleArrayFilter}
              updateFilter={updateFilter}
              updateSearchInput={updateSearchInput}
            />

            {/* Jobs Table */}
            <div className="lg:col-span-3">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Jobs</CardTitle>
                      <CardDescription>
                        {loading
                          ? 'Loading jobs...'
                          : `${jobs.length} of ${pagination.totalItems} jobs`}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <DataTableViewOptions table={table} />
                      <Button
                        onClick={() => router.push('/dashboard/jobs/new')}
                        size="sm"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add Job
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Bulk Actions */}
                  {selectedRows.length > 0 && (
                    <div className="mb-4 flex items-center space-x-2">
                      <Badge variant="secondary">
                        {selectedRows.length} selected
                      </Badge>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button size="sm" variant="outline">
                            Update Status
                            <ChevronDown className="ml-2 h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuLabel>Update Status</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuCheckboxItem
                            onClick={() => handleBulkStatusUpdate('active')}
                          >
                            Active
                          </DropdownMenuCheckboxItem>
                          <DropdownMenuCheckboxItem
                            onClick={() => handleBulkStatusUpdate('inactive')}
                          >
                            Inactive
                          </DropdownMenuCheckboxItem>
                          <DropdownMenuCheckboxItem
                            onClick={() => handleBulkStatusUpdate('filled')}
                          >
                            Filled
                          </DropdownMenuCheckboxItem>
                          <DropdownMenuCheckboxItem
                            onClick={() => handleBulkStatusUpdate('expired')}
                          >
                            Expired
                          </DropdownMenuCheckboxItem>
                          <DropdownMenuCheckboxItem
                            onClick={() => handleBulkStatusUpdate('cancelled')}
                          >
                            Cancelled
                          </DropdownMenuCheckboxItem>
                        </DropdownMenuContent>
                      </DropdownMenu>

                      <Button
                        onClick={handleBulkRequeueAI}
                        size="sm"
                        variant="outline"
                      >
                        Requeue AI
                      </Button>

                      <Button
                        onClick={handleBulkAirtablePush}
                        size="sm"
                        variant="outline"
                      >
                        Push to Airtable
                      </Button>

                      <Button
                        onClick={handleBulkProcessNow}
                        size="sm"
                        variant="outline"
                      >
                        <Play className="mr-2 h-4 w-4" />
                        Process Now
                      </Button>

                      <Button
                        onClick={handleBulkResetMonitor}
                        size="sm"
                        variant="outline"
                      >
                        Reset Monitor
                      </Button>

                      <Button
                        onClick={handleBulkDelete}
                        size="sm"
                        variant="destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </Button>
                    </div>
                  )}

                  {/* Table */}
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                          <TableRow key={headerGroup.id}>
                            {headerGroup.headers.map((header) => (
                              <TableHead key={header.id}>
                                {header.isPlaceholder
                                  ? null
                                  : flexRender(
                                      header.column.columnDef.header,
                                      header.getContext()
                                    )}
                              </TableHead>
                            ))}
                          </TableRow>
                        ))}
                      </TableHeader>
                      <TableBody>
                        {table.getRowModel().rows?.length ? (
                          table.getRowModel().rows.map((row) => (
                            <TableRow
                              data-state={row.getIsSelected() && 'selected'}
                              key={row.id}
                            >
                              {row.getVisibleCells().map((cell) => (
                                <TableCell key={cell.id}>
                                  {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                  )}
                                </TableCell>
                              ))}
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell
                              className="h-24 text-center"
                              colSpan={columns.length}
                            >
                              No results.
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Pagination */}
                  <DataTablePagination
                    canNextPage={pagination.hasNextPage}
                    canPreviousPage={pagination.hasPreviousPage}
                    currentPage={pagination.currentPage}
                    isServerSide={true}
                    onPageChange={handlePageChange}
                    onPageSizeChange={onPageSizeChange}
                    pageSize={pageSize}
                    showSelection={true}
                    table={table}
                    totalPages={pagination.totalPages}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="sql">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5" />
                Generated SQL Query
              </CardTitle>
              <CardDescription>
                The SQL query generated by the filtering system
                {executionTime && (
                  <>
                    {' '}
                    • Executed in{' '}
                    <Badge variant="secondary">{executionTime}ms</Badge>
                  </>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="overflow-x-auto rounded-md bg-muted p-4 text-sm">
                <code>{sqlQuery || 'No query available'}</code>
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Filter Section Component
interface FilterSectionProps {
  title: string;
  items: readonly string[] | string[];
  selectedItems: string[];
  onToggle: (item: string) => void;
  disabled?: boolean;
  maxHeight?: string;
}

function FilterSection({
  title,
  items,
  selectedItems,
  onToggle,
  disabled,
  maxHeight = '200px',
}: FilterSectionProps) {
  return (
    <div className="space-y-1.5">
      <Label className="font-medium text-xs">{title}</Label>
      <div
        className="space-y-1.5 overflow-y-auto rounded-sm border bg-muted/5 p-2"
        style={{ maxHeight }}
      >
        {items.map((item) => (
          <div className="flex items-center space-x-1.5" key={item}>
            <Checkbox
              checked={selectedItems.includes(item)}
              disabled={disabled}
              id={`${title}-${item}`}
              onCheckedChange={() => onToggle(item)}
            />
            <Label className="text-xs" htmlFor={`${title}-${item}`}>
              {item}
            </Label>
          </div>
        ))}
      </div>
    </div>
  );
}

// Keyword List Component
interface KeywordListProps {
  keywords: string[];
  onRemove: (keyword: string) => void;
  variant?: 'default' | 'destructive';
}

function KeywordList({
  keywords,
  onRemove,
  variant = 'default',
}: KeywordListProps) {
  if (keywords.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-1">
      {keywords.map((keyword) => (
        <Badge className="text-xs" key={keyword} variant={variant}>
          {keyword}
          <Button
            className="ml-1 h-3 w-3 p-0"
            onClick={() => onRemove(keyword)}
            variant="ghost"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      ))}
    </div>
  );
}
