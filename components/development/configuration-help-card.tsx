'use client';

import { HelpCircle } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';

export function ConfigurationHelpCard() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <HelpCircle className="h-5 w-5" />
          Configuration Help
        </CardTitle>
        <CardDescription className="text-xs">
          To enable Airtable integration, configure each job board individually
          in the dashboard:
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <ol className="list-inside list-decimal space-y-2 text-xs">
          <li>
            Create a Personal Access Token at{' '}
            <a
              className="text-primary underline underline-offset-4"
              href="https://airtable.com/developers/web/api/personal-access-tokens"
              rel="noopener noreferrer"
              target="_blank"
            >
              Airtable PAT page
            </a>
          </li>
          <li>Get your Base ID from your Airtable base URL</li>
          <li>Set the table name you want to use</li>
          <li>Configure each job board individually in the dashboard</li>
        </ol>
        <Card>
          <CardContent className="p-4">
            <p className="text-muted-foreground text-sm">
              Airtable credentials are now configured per job board in the
              dashboard. No environment variables needed - everything is stored
              securely in the database.
            </p>
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  );
}
