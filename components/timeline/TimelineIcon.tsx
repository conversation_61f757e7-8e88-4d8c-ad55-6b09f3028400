import {
  Bot,
  CheckCircle,
  Circle,
  Eye,
  Monitor,
  Radio,
  RefreshCw,
  Target,
} from 'lucide-react';
import type { TimelineActivity } from '@/types/timeline';

interface TimelineIconProps {
  activity: TimelineActivity;
}

export const TimelineIcon: React.FC<TimelineIconProps> = ({ activity }) => {
  const getIcon = () => {
    switch (activity.type) {
      case 'created':
        return <Circle className="size-4 text-gray-600" />;
      case 'fetched':
        return <Radio className="size-4 text-gray-600" />;
      case 'processed':
        return <Bot className="size-4 text-gray-600" />;
      case 'synced':
        return <RefreshCw className="size-4 text-gray-600" />;
      case 'posted':
        return <Target className="size-4 text-gray-600" />;
      case 'monitored':
        return <Monitor className="size-4 text-gray-600" />;
      case 'updated':
        return <CheckCircle className="size-4 text-gray-600" />;
      case 'viewed':
        return <Eye className="size-4 text-gray-600" />;
      default:
        return (
          <div className="size-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300" />
        );
    }
  };

  return (
    <div className="relative flex size-6 flex-none items-center justify-center bg-white">
      {getIcon()}
    </div>
  );
};
