import type { TimelineActivity } from '@/types/timeline';
import { classNames } from '@/utils/timeline';
import { TimelineIcon } from './TimelineIcon';

interface TimelineItemProps {
  activity: TimelineActivity;
  isLast: boolean;
}

export const TimelineItem: React.FC<TimelineItemProps> = ({
  activity,
  isLast,
}) => {
  // Format the timestamp for display
  const formatTimestamp = (dateTime: string): string => {
    const date = new Date(dateTime);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    });
  };

  // Build the human-readable activity description
  const description = activity.comment
    ? activity.comment
    : `${activity.type} the job.`;

  return (
    <li className="relative flex gap-x-4">
      <div
        className={classNames(
          isLast ? 'h-6' : '-bottom-6',
          'absolute top-0 left-0 flex w-6 justify-center'
        )}
      >
        <div className="w-px bg-gray-200" />
      </div>

      <TimelineIcon activity={activity} />

      <p className="flex-auto py-0.5 text-gray-500 text-xs/5">
        <span className="font-medium text-gray-900">
          {activity.person.name}
        </span>{' '}
        {description}
      </p>

      <time
        className="flex-none py-0.5 text-gray-500 text-xs/5"
        dateTime={activity.dateTime}
      >
        {activity.date} ({formatTimestamp(activity.dateTime)})
      </time>
    </li>
  );
};
