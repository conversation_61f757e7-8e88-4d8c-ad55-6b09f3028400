import type { DatabaseJob } from '@/lib/storage';
import { generateTimelineActivities } from '@/utils/timeline';
import { TimelineItem } from './TimelineItem';

interface JobTimelineProps {
  job: DatabaseJob;
}

export const JobTimeline: React.FC<JobTimelineProps> = ({ job }) => {
  const activities = generateTimelineActivities(job);

  return (
    <div className="rounded-lg bg-white shadow">
      <div className="border-gray-200 border-b px-6 py-4">
        <h2 className="font-semibold text-gray-900 text-lg">
          Activity Timeline
        </h2>
        <p className="mt-1 text-gray-600 text-sm">
          Track all activities and changes related to this job posting.
        </p>
      </div>

      <div className="p-6">
        <ul className="space-y-6">
          {activities.map((activity, index) => (
            <TimelineItem
              activity={activity}
              isLast={index === activities.length - 1}
              key={activity.id}
            />
          ))}
        </ul>
      </div>
    </div>
  );
};
