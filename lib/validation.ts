import { z } from 'zod';
import { INPUT_LIMITS } from './constants';

// Schema for extract API request
export const ExtractRequestSchema = z.object({
  content: z
    .string()
    .min(1, 'Content is required')
    .max(
      INPUT_LIMITS.CONTENT_MAX_CHARS,
      `Content must be less than ${INPUT_LIMITS.CONTENT_MAX_CHARS} characters`
    ),
  sourceUrl: z.string().url().optional(),
});

// Airtable validation utilities
const AIRTABLE_BASE_ID_REGEX = /^app[A-Za-z0-9]{14}$/;
const AIRTABLE_PAT_REGEX = /^pat[A-Za-z0-9.]{40,}$/;

/**
 * Validates Airtable base ID format
 * Valid format: app + 14 alphanumeric characters (e.g., apprhCjWTxfG3JX5p)
 */
export function validateAirtableBaseId(baseId: string): boolean {
  return AIRTABLE_BASE_ID_REGEX.test(baseId);
}

/**
 * Validates Airtable PAT token format
 * Valid format: pat + 40+ characters
 */
export function validateAirtablePat(pat: string): boolean {
  return AIRTABLE_PAT_REGEX.test(pat);
}

/**
 * Validates Airtable table name
 * Must be non-empty and reasonable length
 */
export function validateAirtableTableName(tableName: string): boolean {
  return tableName.length > 0 && tableName.length <= 100;
}

// Extract job request schema - updated to support updating existing jobs
export const ExtractJobRequestSchema = z.object({
  jobId: z.string().uuid().optional(), // If provided, update existing job
  sourcedAt: z.string().datetime().optional(),
  sourceUrl: z.string().url().optional(),
  content: z
    .string()
    .min(1, 'Content is required')
    .max(
      INPUT_LIMITS.CONTENT_MAX_CHARS,
      `Content must be less than ${INPUT_LIMITS.CONTENT_MAX_CHARS} characters`
    ),
});

export type ExtractJobRequest = z.infer<typeof ExtractJobRequestSchema>;
