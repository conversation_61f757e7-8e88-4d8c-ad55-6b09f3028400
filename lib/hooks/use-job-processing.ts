import { useCallback, useState } from 'react';
import { logger } from '@/lib/utils';

interface ProcessingState {
  [jobId: string]: {
    isProcessing: boolean;
    error?: string;
  };
}

export function useJobProcessing() {
  const [processingState, setProcessingState] = useState<ProcessingState>({});

  const processJob = useCallback(
    async (jobId: string, onSuccess?: () => void) => {
      // Set processing state
      setProcessingState((prev) => ({
        ...prev,
        [jobId]: { isProcessing: true },
      }));

      try {
        const response = await fetch('/api/jobs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'processNow',
            id: jobId,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to process job');
        }

        const result = await response.json();

        // Clear processing state on success
        setProcessingState((prev) => {
          const newState = { ...prev };
          delete newState[jobId];
          return newState;
        });

        // Call success callback if provided
        if (onSuccess) {
          onSuccess();
        }

        logger.info(`Job ${jobId} processed successfully`, result);

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Processing failed';

        // Set error state
        setProcessingState((prev) => ({
          ...prev,
          [jobId]: { isProcessing: false, error: errorMessage },
        }));

        logger.error(`Failed to process job ${jobId}`, { error: errorMessage });
        throw error;
      }
    },
    []
  );

  const clearError = useCallback((jobId: string) => {
    setProcessingState((prev) => {
      const newState = { ...prev };
      delete newState[jobId];
      return newState;
    });
  }, []);

  const isProcessing = useCallback(
    (jobId: string) => {
      return processingState[jobId]?.isProcessing;
    },
    [processingState]
  );

  const getError = useCallback(
    (jobId: string) => {
      return processingState[jobId]?.error;
    },
    [processingState]
  );

  return {
    processJob,
    isProcessing,
    getError,
    clearError,
  };
}
