import { useCallback, useEffect, useRef, useState } from 'react';

interface JobFilters {
  search?: string;
  status?: string;
  processingStatus?: string;
  sourceType?: string;
  jobTypes?: string[];
  workplaceTypes?: string[];
  careerLevels?: string[];
  salaryMin?: number;
  salaryMax?: number;
  salaryCurrencies?: string[];
  includeKeywords?: string[];
  excludeKeywords?: string[];
  countries?: string[];
  languages?: string[];
}

export function useJobFilters() {
  const [filters, setFilters] = useState<JobFilters>({});
  const [keywordInput, setKeywordInput] = useState('');
  const [excludeKeywordInput, setExcludeKeywordInput] = useState('');

  // Separate state for search input to prevent focus loss
  const [searchInput, setSearchInput] = useState('');
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced search effect
  useEffect(() => {
    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout to update filters after 800ms of no typing
    searchTimeoutRef.current = setTimeout(() => {
      setFilters((prev) => ({
        ...prev,
        search: searchInput.trim() || undefined,
      }));
    }, 800);

    // Cleanup timeout on unmount
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchInput]);

  const updateFilter = useCallback(
    (key: keyof JobFilters, value: string | string[] | number | undefined) => {
      setFilters((prev) => ({ ...prev, [key]: value }));
    },
    []
  );

  const updateSearchInput = useCallback((value: string) => {
    setSearchInput(value);
  }, []);

  const toggleArrayFilter = useCallback(
    (key: keyof JobFilters, item: string) => {
      setFilters((prev) => {
        const currentArray = (prev[key] as string[]) || [];
        const newArray = currentArray.includes(item)
          ? currentArray.filter((i) => i !== item)
          : [...currentArray, item];
        return { ...prev, [key]: newArray.length > 0 ? newArray : undefined };
      });
    },
    []
  );

  const addIncludeKeyword = useCallback(() => {
    if (keywordInput.trim()) {
      const current = filters.includeKeywords || [];
      const keywords = keywordInput
        .split(',')
        .map((k) => k.trim())
        .filter((k) => k);
      const newKeywords = [
        ...current,
        ...keywords.filter((k) => !current.includes(k)),
      ];
      updateFilter(
        'includeKeywords',
        newKeywords.length > 0 ? newKeywords : undefined
      );
      setKeywordInput('');
    }
  }, [keywordInput, filters.includeKeywords, updateFilter]);

  const addExcludeKeyword = useCallback(() => {
    if (excludeKeywordInput.trim()) {
      const current = filters.excludeKeywords || [];
      const keywords = excludeKeywordInput
        .split(',')
        .map((k) => k.trim())
        .filter((k) => k);
      const newKeywords = [
        ...current,
        ...keywords.filter((k) => !current.includes(k)),
      ];
      updateFilter(
        'excludeKeywords',
        newKeywords.length > 0 ? newKeywords : undefined
      );
      setExcludeKeywordInput('');
    }
  }, [excludeKeywordInput, filters.excludeKeywords, updateFilter]);

  const removeKeyword = useCallback(
    (type: 'includeKeywords' | 'excludeKeywords', keyword: string) => {
      const current = (filters[type] as string[]) || [];
      const newKeywords = current.filter((k) => k !== keyword);
      updateFilter(type, newKeywords.length > 0 ? newKeywords : undefined);
    },
    [filters, updateFilter]
  );

  const clearAllFilters = useCallback(() => {
    setFilters({});
    setKeywordInput('');
    setExcludeKeywordInput('');
    setSearchInput('');
  }, []);

  const getActiveFilterCount = useCallback(() => {
    return Object.keys(filters).filter(
      (key) =>
        filters[key as keyof JobFilters] !== undefined &&
        filters[key as keyof JobFilters] !== '' &&
        filters[key as keyof JobFilters] !== 'all' &&
        !(
          Array.isArray(filters[key as keyof JobFilters]) &&
          (filters[key as keyof JobFilters] as string[]).length === 0
        )
    ).length;
  }, [filters]);

  const hasAnyFilters = useCallback(() => {
    return Object.keys(filters).some(
      (key) =>
        filters[key as keyof JobFilters] !== undefined &&
        filters[key as keyof JobFilters] !== '' &&
        filters[key as keyof JobFilters] !== 'all' &&
        !(
          Array.isArray(filters[key as keyof JobFilters]) &&
          (filters[key as keyof JobFilters] as string[]).length === 0
        )
    );
  }, [filters]);

  return {
    filters,
    keywordInput,
    excludeKeywordInput,
    searchInput,
    setKeywordInput,
    setExcludeKeywordInput,
    updateFilter,
    updateSearchInput,
    toggleArrayFilter,
    addIncludeKeyword,
    addExcludeKeyword,
    removeKeyword,
    clearAllFilters,
    getActiveFilterCount,
    hasAnyFilters,
  };
}
